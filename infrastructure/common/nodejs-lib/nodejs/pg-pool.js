const {Pool, types} = require('pg')

/**
 * PostgreSQL query result handler
 */
const pgBase = {
  thenQueryResult: (result, resolve, reject, MESSAGE) => {
    if (
      result.rows.length > 0 &&
      typeof result.rows[0].result !== 'undefined' &&
      typeof result.rows[0].message !== 'undefined'
    ) {
      if (result.rows[0].result) {
        return resolve({})
      }

      if (
        typeof MESSAGE === 'undefined' ||
        typeof MESSAGE[result.rows[0].message] === 'undefined'
      ) {
        const response = {
          status: result.rows[0].status,
          message: result.rows[0].message,
        }

        return reject(response)
      }
      if (result.rows[0].status === 400) {
        const response = {
          status: result.rows[0].status,
          errorMessage:
            (result.rows[0].localize_error
              ? result.rows[0].localize_error
              : '') + MESSAGE[result.rows[0].message],
          sqlValidateError: true,
        }

        return reject(response)
      }

      // Handle other errors
      const response = {
        status: result.rows[0].status,
        message: MESSAGE[result.rows[0].message],
      }
      return reject(response)
    }

    // Return normal query results
    return resolve(result.rows)
  },

  catchQueryResult: (error, resolve, reject) => {
    return reject(error.message)
  },
}

/**
 * PostgreSQL client wrapper with RLS support
 */
class PgClient {
  constructor(client, _done = null) {
    this.client = client
    this.done = _done
  }

  /**
   * Set tenant context for Row-Level Security(TODO:検討する)
   * @param {number|string} tenantId - Tenant ID to set for RLS
   * @param {boolean} bypassRls - Whether to bypass RLS (default: false)
   * @returns {Promise} - Promise that resolves when RLS is configured
   */
  setTenantContext(tenantId, bypassRls = false) {
    const tenantIdStr = String(tenantId)
    const bypassRlsStr = bypassRls ? 'on' : 'off'

    console.log(
      `RLS: Setting context - tenant: ${tenantIdStr}, bypass: ${bypassRlsStr}`
    )

    const rlsQuery = `
      SELECT set_config('app.current_tenant', $1, TRUE),
             set_config('app.bypass_rls', $2, TRUE);
    `

    return this.client
      .query(rlsQuery, [tenantIdStr, bypassRlsStr])
      .then(() => {
        console.log(`RLS: Context set for tenant ${tenantIdStr}`)
      })
      .catch(error => {
        console.error('❌ RLS: Failed to set context:', error)
        throw error
      })
  }

  query(...args) {
    return new Promise((resolve, reject) => {
      this.client
        .query(...args)
        .then(result => pgBase.thenQueryResult(result, resolve, reject))
        .catch(error => pgBase.catchQueryResult(error, resolve, reject))
    })
  }

  release() {
    if (this.done) {
      this.done()
    }
  }

  end() {
    return this.client.end()
  }
}

/**
 * PostgreSQL connection pool with RLS support
 */
class PgPool {
  constructor(pghost = process.env.PGHOST) {
    // Configure PostgreSQL type parsers
    types.setTypeParser(types.builtins.INT8, value =>
      Number.parseInt(value, 10)
    )
    types.setTypeParser(types.builtins.FLOAT8, value =>
      Number.parseFloat(value)
    )
    types.setTypeParser(types.builtins.NUMERIC, value =>
      Number.parseFloat(value)
    )

    this.PGHOST = pghost
    this.setPool(pghost)

    // RLS context
    this.tenantId = null
    this.bypassRls = false
    this.rlsEnabled = true
  }

  setPool(pghost) {
    this.pool = new Pool({
      host: pghost,
      ssl: {
        rejectUnauthorized: false,
      },
      idleTimeoutMillis: 1,
      connectionTimeoutMillis: 5000,
    })
    this.pool.on('error', error => {
      console.error('PostgreSQL pool error:', error)
    })
  }

  getPoolClient() {
    return new Promise((resolve, reject) => {
      this.pool.connect((err, client, done) => {
        if (err) {
          return reject(err)
        }
        return resolve(new PgClient(client, done))
      })
    })
  }

  setMessage(message) {
    this.MESSAGE = message
  }

  /**
   * Legacy query method for backward compatibility
   * WARNING: This bypasses RLS - use rlsQuery() for tenant-aware queries
   * @param {string} sql - SQL query string
   * @param {Array} params - Query parameters (optional)
   * @returns {Promise} - Promise that resolves to query result
   */
  // TODO: just use for test. this method after setting cognito
  query(sql, params = []) {
    console.warn(
      '❌ Using legacy query() method that bypasses RLS(use for test only). Consider using rlsQuery() instead.'
    )
    return new Promise((resolve, reject) => {
      this.pool.connect((err, client, done) => {
        if (err) {
          console.error('❌ Failed to get database connection:', err)
          return reject(err)
        }

        client
          .query(sql, params)
          .then(result => {
            done()
            resolve(result.rows)
          })
          .catch(error => {
            done()
            console.error('❌ Query failed:', error)
            reject(error)
          })
      })
    })
  }

  /**
   * Validate tenant ID parameter
   * @param {any} tenantId - Tenant ID to validate
   * @returns {string} - Validated tenant ID as string
   * @private
   */
  _validateTenantId(tenantId) {
    if (tenantId === null || tenantId === undefined || tenantId === '') {
      throw new Error('RLS: Tenant ID is required for all database queries')
    }
    if (typeof tenantId !== 'string' && typeof tenantId !== 'number') {
      throw new Error('RLS: Tenant ID must be a string or number')
    }
    const tenantIdStr = String(tenantId)
    if (tenantIdStr.trim() === '') {
      throw new Error('RLS: Tenant ID cannot be empty')
    }
    return tenantIdStr
  }

  /**
   * Query with RLS context per transaction.
   * Automatically sets tenant context, auto commits or rolls back transaction.
   * @param {string|number} tenantId - Tenant ID (REQUIRED as first parameter)
   * @param {string} sql - SQL query string
   * @param {Array} params - Query parameters (optional)
   * @param callback - promise callback function to execute with the client
   * @returns {Promise} - Promise that resolves to query result
   */
  rlsQueryTx(tenantId, callback) {
    return this.getPoolClient().then(client => {
      console.log(
        `RLS: Setting tenant context for connection - tenant: ${tenantId}`
      )

      client
        .query('BEGIN')
        .then(() => {
          // Set RLS context for this connection, then execute query
          const validatedTenantId = this._validateTenantId(tenantId)
          const rlsQuery = `SELECT set_config('app.current_tenant', $1, TRUE);`
          return client.query(rlsQuery, [validatedTenantId])
        })
        .then(() => {
          console.log(
            `RLS: Context set, executing query for tenant ${tenantId}`
          )
          return callback(client) // Callback is expected to be a promise-returning function
        })
        .then(result => {
          // Commit transaction if no errors
          return client.query('COMMIT').then(() => {
            console.log(
              `✅ RLS: Transaction committed successfully for tenant ${tenantId}`
            )
            return Promise.resolve(result)
          })
        })
        .catch(error => {
          // Rollback on query error
          console.error(`❌ RLS: Query failed for tenant ${tenantId}:`, error)
          // Rollback transaction
          return client
            .query('ROLLBACK')
            .then(() => {
              console.log(
                `❌ RLS: Transaction rolled back for tenant ${tenantId}`
              )
              return pgBase.catchQueryResult(error, resolve, reject)
            })
            .catch(rollbackError => {
              console.error(
                `❌ RLS: Rollback failed for tenant ${tenantId}:`,
                rollbackError
              )
            })
        })
        .finally(() => {
          if (client) client.release() // Always release connection, even on error
        })
    })
  }

  /**
   * Main query method with mandatory RLS tenant ID
   * @param {string|number} tenantId - Tenant ID (REQUIRED as first parameter)
   * @param {string} sql - SQL query string
   * @param {Array} params - Query parameters (optional)
   * @returns {Promise} - Promise that resolves to query result
   */
  rlsQuery(tenantId, sql, params = []) {
    try {
      const validatedTenantId = this._validateTenantId(tenantId)
      console.log(`SQL Query [Tenant: ${validatedTenantId}]:`, sql, params)
      return this._executeQueryWithRLS(validatedTenantId, sql, params)
    } catch (error) {
      console.error('❌ Query validation error:', error)
      return Promise.reject(error)
    }
  }

  /**
   * Execute query with RLS context per connection
   * @param {string} tenantId - Validated tenant ID
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise} - Promise that resolves to query result
   * @private
   */
  _executeQueryWithRLS(tenantId, sql, params) {
    return this.getPoolClient().then(client => {
      console.log(
        `RLS: Setting tenant context for connection - tenant: ${tenantId}`
      )
      return Promise.resolve()
        .then(() => {
          return client.query('BEGIN')
        })
        .then(() => {
          // Set RLS context for this connection, then execute query
          const rlsQuery = `SELECT set_config('app.current_tenant', $1, TRUE);`

          return client
            .query(rlsQuery, [tenantId])
            .then(() => {
              console.log(
                `RLS: Context set, executing query for tenant ${tenantId}`
              )
              return client.query(sql, params)
            })
            .then(result => {
              console.log(
                `✅ RLS: Query executed successfully for tenant ${tenantId}`
              )
              return Promise.resolve(result)
            })
        })
        .then(result => {
          return client.query('COMMIT').then(() => {
            return Promise.resolve(result)
          })
        })
        .catch(error => {
          console.error(`❌ RLS: Query failed for tenant ${tenantId}:`, error)
          // Rollback transaction on error
          return client.query('ROLLBACK').then(() => {
            console.log(
              `❌ RLS: Transaction rolled back for tenant ${tenantId}`
            )
            // Reject the promise with the error
            return Promise.reject(error)
          })
        })
        .finally(() => {
          client.release() // Always release connection, even on error
        })
    })
  }

  /**
   * Query with RLS bypass for admin operations
   * @param {string|number} tenantId - Tenant ID (REQUIRED as first parameter)
   * @param {string} sql - SQL query string
   * @param {Array} params - Query parameters (optional)
   * @returns {Promise} - Promise that resolves to query result
   */
  byPassQuery(tenantId, sql, params = []) {
    return new Promise((resolve, reject) => {
      try {
        const validatedTenantId = this._validateTenantId(tenantId)
        console.log(
          `SQL Query [Tenant: ${validatedTenantId}, BYPASS RLS]:`,
          sql,
          params
        )
        this._executeQueryWithBypass(validatedTenantId, sql, params)
          .then(result =>
            pgBase.thenQueryResult(result, resolve, reject, this.MESSAGE)
          )
          .catch(error => pgBase.catchQueryResult(error, resolve, reject))
      } catch (error) {
        console.error('❌ Query validation error:', error)
        return pgBase.catchQueryResult(error, resolve, reject)
      }
    })
  }

  /**
   * Execute query with RLS bypass for admin operations
   * @param {string} tenantId - Validated tenant ID
   * @param {string} sql - SQL query
   * @param {Array} params - Query parameters
   * @returns {Promise} - Promise that resolves to query result
   * @private
   */
  _executeQueryWithBypass(tenantId, sql, params) {
    return new Promise((resolve, reject) => {
      this.pool.connect((err, client, done) => {
        if (err) {
          console.error('❌ RLS: Failed to get database connection:', err)
          return reject(err)
        }
        console.log(
          `🔓RLS: Setting bypass context for connection - tenant: ${tenantId}`
        )

        // Set both tenant context and bypass flag
        const rlsQuery = `
          SELECT set_config('app.current_tenant', $1, TRUE),
                 set_config('app.bypass_rls', 'on', TRUE);
        `

        client
          .query(rlsQuery, [tenantId])
          .then(() => {
            console.log(
              `RLS: Bypass context set, executing query for tenant ${tenantId}`
            )
            return client.query(sql, params)
          })
          .then(result => {
            done()
            resolve(result)
          })
          .catch(error => {
            done()
            console.error(
              `❌ RLS: Bypass query failed for tenant ${tenantId}:`,
              error
            )
            reject(error)
          })
      })
    })
  }

  /**
   * Close the connection pool
   */
  end() {
    return new Promise((resolve, reject) => {
      this.pool.end(err => {
        if (err) {
          return reject(err)
        }
        return resolve()
      })
    })
  }
}

module.exports = PgPool
