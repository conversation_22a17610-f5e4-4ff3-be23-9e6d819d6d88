const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const moment = require('moment')
const pool = new PgPool()

// Batch functions need a system tenant ID for cross-tenant operations
const SYSTEM_TENANT_ID = 'SYSTEM_BATCH'

exports.handle = (e, ctx, cb) => {
  moment.locale('ja')
  return Promise.resolve()
    .then(() => {
      // Use bypass RLS for batch function that needs to see all tenants
      return pool.byPassQuery(
        SYSTEM_TENANT_ID,
        'SELECT * FROM "f_batch_auction_start_alert"();'
      )
    })
    .then(exhs => {
      if (typeof exhs === 'undefined' || exhs === null || exhs.length === 0) {
        console.log('No auction start alerts found')
        return Promise.resolve()
      }

      const tenantNos = []
      exhs.forEach(exh => {
        if (!tenantNos.includes(exh.tenant_no)) {
          tenantNos.push(exh.tenant_no)
        }
      })
      console.log('tenantNos: ', tenantNos)

      return Promise.all(
        tenantNos.map(tenantNo => {
          console.log('tenantNo: ', tenantNo)

          const exhibitions = exhs.filter(exh => exh.tenant_no === tenantNo)
          console.log('exhibitions: ', exhibitions)

          return Promise.resolve()
            .then(() => {
              // Use normal RLS for tenant-specific constant queries
              return pool.rlsQuery(
                tenantNo,
                'SELECT * FROM "f_get_constant_by_key_string"($1,$2);',
                [
                  tenantNo,
                  [
                    'EMAIL_AUCTION_START_FOR_MEMBER',
                    'EMAIL_COMMON_FOOTER',
                    'EMAIL_FROM',
                  ],
                ]
              )
            })
            .then(constants => {
              const members = exhibitions.map(x => x.members).flat()
              const argumentList = members.map(member => {
                const mailFrom =
                  constants.find(
                    x =>
                      x.key_string === 'EMAIL_FROM' &&
                      x.language_code === member.language_code
                  )?.value2 || null
                const mail =
                  constants.find(
                    x =>
                      x.key_string === 'EMAIL_AUCTION_START_FOR_MEMBER' &&
                      x.language_code === member.language_code
                  ) || {}
                const footer =
                  constants.find(
                    x =>
                      x.key_string === 'EMAIL_COMMON_FOOTER' &&
                      x.language_code === member.language_code
                  ) || {}
                const title = mail.value1
                const sender = mailFrom
                  ? `"${mailFrom}"<${mail.value2}>`
                  : mail.value2
                const receivers = [member.email]

                const content = Common.format(mail.value4, [
                  member.exhibition_name,
                  footer.value4 || '',
                ])

                return [title, content, sender, receivers]
              })

              return Common.splitAll(
                Common.sendMailBySES,
                argumentList,
                200, // splitAmount : 200
                2 // waitSeconds: 2
              ).then(result => {
                console.log('🚀 All emails sent successfully', result)
                return Promise.resolve()
              })
            })
        })
      )
    })
    .then(() => {
      return cb(null, {result: 'successfully'})
    })
    .catch(error => {
      console.error('Execution error:', error)
      return Common.createErrorResponse(cb, error)
    })
}
