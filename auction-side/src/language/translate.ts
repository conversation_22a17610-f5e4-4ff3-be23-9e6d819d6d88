import {en as enLocale, ja as ja<PERSON>ocale} from 'vuetify/locale'

export const translate: Translate = {
  ja: {
    $vuetify: {
      ...jaLocale,
      dataIterator: {
        rowsPerPageText: '表示件数:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: '戻る',
    COMMON_BACK_LIST: '一覧へ戻る',
    COMMON_MORE: 'もっと見る',
    COMMON_JAPAN: '日本',
    COMMON_DUBAI: 'ドバイ',
    COMMON_HONGKONG: '香港',
    COMMON_SEND: '送信する',
    COMMON_AGREE: '同意する',
    COMMON_ERROR: 'エラーが発生しました。',
    COMMON_CONFIRM: '確認',
    COMMON_INPUT_ERROR: '入力エラー',
    COMMON_DATE_FORMAT: 'yyyy年MM月dd日',
    COMMON_REMOVE: '削除',
    COMMON_UPDATE_AUCTION: '更新',
    COMMON_DAY: '日',
    COMMON_HOUR: '時間',
    COMMON_MINUTE: '分',
    COMMON_SECOND: '秒',
    ASCENDING_AUCTION: '競り上がり式オークション',
    SEALED_AUCTION: '封印入札式オークション',

    // Site info
    SITE_TITLE: 'AUCTION │ クラウドECオークション',
    COPYRIGHT: 'Copyright (c) 2004-2025, GMO MAKESHOP Co. Ltd. All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'カテゴリを選択',
    TOP_APP_BAR_REGISTER: '新規登録',
    TOP_APP_BAR_LOGIN: 'ログイン',
    TOP_APP_BAR_LOGOUT: 'ログアウト',

    // Header navigation
    HEADER_NAV_PRODUCT_CATEGORY: '商品カテゴリー',
    HEADER_NAV_ALL_CATEGORIES: 'すべてのカテゴリー',
    HEADER_NAV_CATEGORY_1: 'カテゴリー1',
    HEADER_NAV_CATEGORY_2: 'カテゴリー2',
    HEADER_NAV_CATEGORY_3: 'カテゴリー3',
    HEADER_NAV_SEARCH_PLACEHOLDER: 'キーワードで検索',
    HEADER_NAV_SITE_ABOUT: 'サイトについて',
    HEADER_NAV_FIRST_TIME_VISITORS: '初めてご利用の方へ',
    HEADER_NAV_SHOPPING_GUIDE: 'ご利用ガイド',
    HEADER_NAV_FAQ: 'よくあるご質問',
    HEADER_NAV_CONTACT_US: 'お問い合わせ',
    HEADER_NAV_MEMBER_SERVICES: '会員サービス',
    HEADER_NAV_LOGIN: 'ログイン',
    HEADER_NAV_FAVORITES: 'お気に入り',
    HEADER_NAV_BIDDING: '入札中',
    HEADER_NAV_SUCCESSFUL_BID_HISTORY: '落札履歴',
    HEADER_NAV_REGISTER: '新規登録',
    HEADER_NAV_LOGOUT: 'ログアウト',
    HEADER_NAV_NEW_MEMBER_REGISTER: '新規会員登録',
    HEADER_NAV_MEMBER_MENU: '会員メニュー',
    HEADER_NAV_MY_PAGE: 'マイページ',
    HEADER_NAV_GUIDANCE: 'ご案内',
    HEADER_NAV_COMPANY_INFO: '会社情報',
    HEADER_NAV_TOKUSHO: '特商法に基づく表記',
    HEADER_NAV_TERMS_OF_SERVICE: '利用規約',
    HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: '特定商取引法に基づく表記',
    HEADER_NAV_PRIVACY_POLICY: 'プライバシーポリシー',

    ROUTE_TOP: 'トップ',

    // Product detail
    DETAIL_TITLE: '商品詳細',
    DETAIL_DESCRIPTION: '商品説明',
    DETAIL_CURRENCY: '円',
    DETAIL_QUANTITY: '出品数量',
    DETAIL_LOWEST_BID_QUANTITY: '最低入札数量',
    DETAIL_LOWEST_BID_PRICE: '最低入札単価',
    DETAIL_BID_COUNT: '入札件数',
    DETAIL_BID_QUANTITY: '入札数量',
    DETAIL_BID_UNIT_PRICE: '入札単価',
    DETAIL_BID_PRICE_FOR_ASC_AUCTION: '入札価格',
    DETAIL_BID_TOTAL_PRICE: '入札合計金額',
    DETAIL_BID_BUTTON: '入札する',
    DETAIL_CONTACT_BUTTON: 'この商品に関するお問い合わせ',
    DETAIL_ABOUT_RANK: '商品状態ランクについて',
    DETAIL_CHAT: 'チャットで質問する',
    DETAIL_VIEW_COMMENTS: '質問コメントを見る',

    // Product detail info
    DETAIL_INFO_MAKER: 'メーカー',
    DETAIL_INFO_PRODUCT_NAME: '商品名',
    DETAIL_INFO_SIM: 'SIM',
    DETAIL_INFO_CAPACITY: '容量',
    DETAIL_INFO_COLOR: '色',
    DETAIL_INFO_RANK: 'グレード',
    DETAIL_INFO_QUANTITY: '数量',
    DETAIL_INFO_NOTE1: '備考1',
    DETAIL_INFO_NOTE2: '備考2',
    DETAIL_INFO_LOWEST_BID_PRICE: '最低入札<br>単価',
    DETAIL_INFO_LOWEST_BID_QUANTITY: '最低入札<br>数量',
    DETAIL_INFO_FAVORITE: 'お気に入り',
    DETAIL_INFO_START_PRICE: 'スタート価格',
    DETAIL_INFO_CURRENT_PRICE: '現在価格',
    DETAIL_INFO_TAX_INCLUDED_PRICE: '税込価格',
    DETAIL_INSTANT_PRICE: '即決価格',

    // Error messages（エラーメッセージ）
    ERROR_PRICE_EMPTY: '入札価格を入力してください。',
    ERROR_PRICE_INVALID_FORMAT: '入札価格は数字のみを入力してください。',
    ERROR_PRICE_MAX_LENGTH: '入札価格は{0}桁以下の数字で入力してください。',
    ERROR_LOWEST_UNIT_PRICE_INVALID: '最低入札単価以上の数値を入力してください。',
    ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: '現在価格以下の入札はできません。',
    ERROR_QUANTITY_EMPTY: '入札数量を入力してください。',
    ERROR_QUANTITY_INVALID_FORMAT: '入札数量は数字のみを入力してください。',
    ERROR_QUANTITY_MAX_LENGTH: '入札数量は{0}桁以下の数字で入力してください。',
    ERROR_LOWEST_BID_QUANTITY_INVALID: '最低入札数量以上の数値を入力してください。',
    ERROR_BID_QUANTITY_EXCEEDS_MAX: '入札数量が出品数量を超えています。',
    ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE: '会員限定で入札できます。ログインしてください。',

    // Filter box
    FILTER_BOX_TITLE: '検索条件',
    FILTER_BOX_INPUT_PLACEHOLDER: 'メーカー・商品名を入力',
    FILTER_BOX_KEYWORD: 'キーワード',
    FILTER_BOX_SEARCH_BUTTON: 'この条件で検索する',
    FILTER_BOX_CATEGORY: 'カテゴリ',
    FILTER_BOX_CLEAR_CONDITIONS: '条件をクリア',
    FILTER_BOX_AUCTION_COUNT: '件のオークション',
    FILTER_BOX_SEARCH_CRITERIA: '検索条件',
    SEARCH_RESULT: '検索結果',
    PRODUCT_LIST: '商品一覧',

    MYPAGE_AUCTION_COUNT: '件のオークション',

    // Favorite
    FAVORITE_TITLE: 'お気に入り',
    FAVORITE_EMPTY: 'お気に入りに商品がありません。',
    FAVORITE_CLEAR_PRICE_INPUT: '入力値クリア',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: '入札小計',
    FAVORITE_SUB_TOTAL_BID_PRICE: '入札小計',
    FAVORITE_LOGIN_REQUIRED_FAVORITE: 'ログイン後にお気に入りリストに入れられます。',
    FAVORITE_BID_BUTTON: '入札する',
    FAVORITE_RE_BID_BUTTON: '再入札する',
    FAVORITE_BID_QUANTITY: '入札数量',
    FAVORITE_DELETE_FAVORITE1: 'お気に入り',
    FAVORITE_DELETE_FAVORITE2: 'から削除',

    // Bid history
    BID_HISTORY_END_DATE: '落札日',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: '落札単価',
    BID_HISTORY_BID_SUCCESS_PRICE: '落札価格',
    BID_HISTORY_BID_SUCCESS_QUANTITY: '落札数',
    BID_HISTORY_BID_TOTAL_PRICE: '合計金額',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'ログアウトしますか？',
    AUTH_LOGOUT: 'ログアウト',
    AUTH_CLOSE: '閉じる',
    AUTH_CANCEL: 'キャンセル',

    // Login
    LOGIN_TITLE: 'ログイン',
    LOGIN_EMAIL: 'メールアドレス',
    LOGIN_EMAIL_CONFIRM: 'メールアドレス(確認用)',
    LOGIN_PASSWORD: 'パスワード',
    LOGIN_SAVE_LOGIN_INFO: 'メールアドレス・パスワードを保存',
    LOGIN_FORGET_PASSWORD: 'パスワードをお忘れの方',
    LOGIN_RULE: '参加規約',
    LOGIN_AGREE_RULE: '参加規約に同意する',
    LOGIN_AGREE: '同意する',
    LOGIN_ENTRY_INFO1: '新規会員登録(無料)',
    LOGIN_ENTRY_INFO2: '商品への入札は会員登録が必要です。',
    LOGIN_CONFIRM_BUTTON: 'ログイン',
    LOGIN_PASSWORD_HINT: '8～16文字の半角英数字',

    // Password Reminder
    LOGIN_REMINDER_TITLE: 'パスワードをお忘れの方',
    LOGIN_REMINDER_SUBTITLE: 'Reminder',
    LOGIN_REMINDER_MESSAGE1:
      'パスワードをお忘れの方は登録したIDとメールアドレスを入力してください。',
    LOGIN_REMINDER_MESSAGE2:
      '「送信」ボタンを押しますと、パスワードが登録メールアドレスに届きます。',
    LOGIN_REMINDER_FORGOT_ID: 'IDをお忘れの場合は、',
    LOGIN_REMINDER_CONTACT_LINK: 'お問い合わせフォーム',
    LOGIN_REMINDER_CONTACT_SUFFIX: 'からお問い合わせください。',
    LOGIN_REMINDER_USER_ID: 'ID',
    LOGIN_REMINDER_USER_ID_PLACEHOLDER: '半角英数字',
    LOGIN_REMINDER_SEND_BUTTON: '送信する',
    LOGIN_REMINDER_COMPLETE_MESSAGE: '新しいパスワードをメールアドレス宛に送信いたしました。',
    LOGIN_REMINDER_BACK_TO_LOGIN: 'ログイン画面へ戻る',
    LOGIN_REMINDER_EMAIL_ERROR: 'メールアドレスが正しくありません',
    LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: 'メールアドレスが一致しません',

    MYPAGE_EDIT_PROFILE: '会員情報編集',
    MYPAGE_EDIT_CONFIRM: '会員情報編集確認',
    MYPAGE_CARD: 'カード情報',
    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: '入札',
    BID_COUNT: '件',
    CLASSIFICATION_ASCENDING: '競り上がり入札',
    CLASSIFICATION_SEALED: '封印入札',
    ASCENDING: '競り上がり入札',
    SEALED: '封印入札',
    BID_STATUS_INPROGRESS: '入札受付中',
    BID_STATUS_CANCEL: '出品停止',
    BID_STATUS_NOT_START_YET: '開始待ち',
    BID_STATUS_ENDED: 'オークション終了',
    BID_STATUS_PRE_AUCTION: '下見期間中',
    BID_STATUS_EXTENDING: 'オークション延長中',
    HIGHEST_BIDDER: '最高入札者',
    HIGHEST_BID_AMOUNT: '最高入札額',
    BID_STATUS: 'ステータス',
    REMAINING_TIME: '残り時間',
    YOU_ARE_TOP: 'あなたがTOP',
    RESERVE_PRICE_NOT_MET: '最低落札価格に達していません',
    RESERVE_PRICE_EXCEEDED: '最低落札超え',
    MORE_LITTLE: 'あと少し',
    SECOND_BIDDER: '2位入札者',
    END_DATE_TIME: '終了日時',
    START_DATE_TIME: '開始日時',
    RECORDED_BID_PRICE: '入札済み価格',

    // Date picker
    DATE_PICKER_DATE_FORMAT: 'yyyy年MM月dd日',
    DATE_PICKER_YEAR: '年',
    DATE_PICKER_MONTH: '月',
    DATE_PICKER_DAY: '日',

    // Custom translation(jp key)
    単位で入札できます: '単位で入札できます',
    終了予定: '終了予定',
    この商品をシェア: 'この商品をシェア',
  },

  en: {
    $vuetify: {
      ...enLocale,
      dataIterator: {
        rowsPerPageText: 'Number of items displayed:',
        pageText: '{0}-{1} of {2}',
      },
    },

    // Common translations
    COMMON_BACK: 'Back',
    COMMON_BACK_LIST: 'Back to List',
    COMMON_MORE: 'Show More',
    COMMON_JAPAN: 'Japan',
    COMMON_DUBAI: 'Dubai',
    COMMON_HONGKONG: 'Hong Kong',
    COMMON_SEND: 'Send',
    COMMON_AGREE: 'Agree',
    COMMON_ERROR: 'Error occurred.',
    COMMON_CONFIRM: 'Confirm',
    COMMON_INPUT_ERROR: 'Input Error',
    COMMON_DATE_FORMAT: 'yyyy/MM/dd',
    COMMON_REMOVE: 'Remove',
    COMMON_UPDATE_AUCTION: 'Update',
    COMMON_DAY: 'd',
    COMMON_HOUR: 'h',
    COMMON_MINUTE: 'm',
    COMMON_SECOND: 's',
    ASCENDING_AUCTION: 'Ascending Auction',
    SEALED_AUCTION: 'Sealed Bid Auction',

    // Site info
    SITE_TITLE: 'AUCTION │ クラウドECオークション',
    COPYRIGHT: 'Copyright (c) 2004-2025, GMO MAKESHOP Co. Ltd. All Rights Reserved.',

    // Top page app bar
    TOP_APP_BAR_SELECT_CATEGORY: 'Select Category',
    TOP_APP_BAR_REGISTER: 'New Member Registration',
    TOP_APP_BAR_LOGIN: 'Sign In',
    TOP_APP_BAR_LOGOUT: 'Sign Out',

    // Header navigation
    HEADER_NAV_PRODUCT_CATEGORY: 'Category',
    HEADER_NAV_ALL_CATEGORIES: 'All Categories',
    HEADER_NAV_CATEGORY_1: 'Category 1',
    HEADER_NAV_CATEGORY_2: 'Category 2',
    HEADER_NAV_CATEGORY_3: 'Category 3',
    HEADER_NAV_SEARCH_PLACEHOLDER: 'Search by keyword',
    HEADER_NAV_SITE_ABOUT: 'About Site',
    HEADER_NAV_FIRST_TIME_VISITORS: 'First Time Visitors',
    HEADER_NAV_SHOPPING_GUIDE: 'Shopping Guide',
    HEADER_NAV_FAQ: 'FAQ',
    HEADER_NAV_CONTACT_US: 'Contact Us',
    HEADER_NAV_MEMBER_SERVICES: 'Member Services',
    HEADER_NAV_LOGIN: 'Sign In',
    HEADER_NAV_FAVORITES: 'Favorites',
    HEADER_NAV_BIDDING: 'Bidding',
    HEADER_NAV_SUCCESSFUL_BID_HISTORY: 'History',
    HEADER_NAV_REGISTER: 'Register',
    HEADER_NAV_LOGOUT: 'Sign Out',
    HEADER_NAV_NEW_MEMBER_REGISTER: 'New Member Registration',
    HEADER_NAV_MEMBER_MENU: 'Member Menu',
    HEADER_NAV_MY_PAGE: 'My Page',
    HEADER_NAV_GUIDANCE: 'Guidance',
    HEADER_NAV_COMPANY_INFO: 'Company Information',
    HEADER_NAV_TOKUSHO: 'Specified Commercial Transaction Law',
    HEADER_NAV_TERMS_OF_SERVICE: 'Terms of Service',
    HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: 'Commercial Transaction Act',
    HEADER_NAV_PRIVACY_POLICY: 'Privacy Policy',

    // Routes
    ROUTE_TOP: 'TOP',

    // Product detail
    DETAIL_TITLE: 'Product Details',
    DETAIL_DESCRIPTION: 'Product Description',
    DETAIL_CURRENCY: 'Yen',
    DETAIL_QUANTITY: 'Quantity',
    DETAIL_LOWEST_BID_QUANTITY: 'Minimum bid quantity',
    DETAIL_LOWEST_BID_PRICE: 'Minimum Bid Unit Price',
    DETAIL_BID_COUNT: 'Number of bids',
    DETAIL_BID_QUANTITY: 'Bid Quantity',
    DETAIL_BID_UNIT_PRICE: 'Bid Unit Price',
    DETAIL_BID_PRICE_FOR_ASC_AUCTION: 'Bid Price',
    DETAIL_BID_TOTAL_PRICE: 'Total Bid Amount',
    DETAIL_BID_BUTTON: 'Place Bid',
    DETAIL_CONTACT_BUTTON: 'Inquiries about this product',
    DETAIL_ABOUT_RANK: 'Product Grade Details',
    DETAIL_CHAT: 'Chat with Seller',
    DETAIL_VIEW_COMMENTS: 'View Comments',

    // Product detail info
    DETAIL_INFO_MAKER: 'Manufacturer',
    DETAIL_INFO_PRODUCT_NAME: 'Product Name',
    DETAIL_INFO_SIM: 'SIM',
    DETAIL_INFO_CAPACITY: 'Capacity',
    DETAIL_INFO_COLOR: 'Color',
    DETAIL_INFO_RANK: 'Grade',
    DETAIL_INFO_QUANTITY: 'Quantity',
    DETAIL_INFO_NOTE1: 'Note 1',
    DETAIL_INFO_NOTE2: 'Note 2',
    DETAIL_INFO_LOWEST_BID_PRICE: 'Minimum Bid Price',
    DETAIL_INFO_LOWEST_BID_QUANTITY: 'Minimum Bid Quantity',
    DETAIL_INFO_FAVORITE: 'Favorite',
    DETAIL_INFO_START_PRICE: 'Starting Price',
    DETAIL_INFO_CURRENT_PRICE: 'Current Price',
    DETAIL_INFO_TAX_INCLUDED_PRICE: 'Price including tax',
    DETAIL_INSTANT_PRICE: 'Buy Now Price',

    // Product detail bid modal
    ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE:
      'Please login to place bids. Bidding is available for members only.',

    // Product detail error messages
    ERROR_PRICE_EMPTY: 'Please enter a bid price.',
    ERROR_PRICE_INVALID_FORMAT: 'Please enter numbers only for the bid price.',
    ERROR_PRICE_MAX_LENGTH: 'Please enter the bid price with {0} digits or less.',
    ERROR_LOWEST_UNIT_PRICE_INVALID:
      'Please enter a value equal to or greater than the minimum bid unit price.',
    ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: 'You cannot bid at or below the current price.',
    ERROR_QUANTITY_EMPTY: 'Please enter a bid quantity.',
    ERROR_QUANTITY_INVALID_FORMAT: 'Please enter numbers only for the bid quantity.',
    ERROR_QUANTITY_MAX_LENGTH: 'Please enter the bid quantity with {0} digits or less.',
    ERROR_LOWEST_BID_QUANTITY_INVALID:
      'Please enter a value equal to or greater than the minimum bid quantity.',
    ERROR_BID_QUANTITY_EXCEEDS_MAX: 'The bid quantity exceeds the available quantity.',

    // Filter box
    FILTER_BOX_TITLE: 'Advanced Search',
    FILTER_BOX_INPUT_PLACEHOLDER: 'Enter manufacturer or product name',
    FILTER_BOX_KEYWORD: 'Keyword',
    FILTER_BOX_SEARCH_BUTTON: 'Search with these conditions',
    FILTER_BOX_CATEGORY: 'Category',
    FILTER_BOX_CLEAR_CONDITIONS: 'Clear the conditions',
    FILTER_BOX_AUCTION_COUNT: 'auctions',
    FILTER_BOX_SEARCH_CRITERIA: 'Search condition',
    SEARCH_RESULT: 'Search Results',
    PRODUCT_LIST: 'Product List',

    MYPAGE_AUCTION_COUNT: 'auctions',

    // Favorite
    FAVORITE_TITLE: 'Favorite',
    FAVORITE_EMPTY: 'Your favorites list is currently empty.',
    FAVORITE_CLEAR_PRICE_INPUT: 'Clear Input',
    FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: 'Bidding<br>Subtotal',
    FAVORITE_SUB_TOTAL_BID_PRICE: 'Bidding Subtotal',
    FAVORITE_LOGIN_REQUIRED_FAVORITE:
      'You can put the item in your favorites list after signing in.',
    FAVORITE_BID_BUTTON: 'Place a bid',
    FAVORITE_RE_BID_BUTTON: 'Rebid',
    FAVORITE_BID_QUANTITY: 'Bid Quantity',
    FAVORITE_DELETE_FAVORITE1: 'Remove from',
    FAVORITE_DELETE_FAVORITE2: 'Favorites',

    // Bid history
    BID_HISTORY_END_DATE: 'End Date',
    BID_HISTORY_BID_SUCCESS_UNIT_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_PRICE: 'Winning Price',
    BID_HISTORY_BID_SUCCESS_QUANTITY: 'Winning Qty',
    BID_HISTORY_BID_TOTAL_PRICE: 'Total Amount',

    // Auth
    AUTH_LOGOUT_MESSAGE: 'Would you like to sign out?',
    AUTH_LOGOUT: 'Sign out',
    AUTH_CLOSE: 'Close',
    AUTH_CANCEL: 'Cancel',

    // Login
    LOGIN_TITLE: 'Sign in',
    LOGIN_EMAIL: 'Email Address',
    LOGIN_EMAIL_CONFIRM: 'Email Address (Confirmation)',
    LOGIN_PASSWORD: 'Password',
    LOGIN_SAVE_LOGIN_INFO: 'Save email address and password',
    LOGIN_FORGET_PASSWORD: 'Forgot Password?',
    LOGIN_RULE: 'Terms of Service',
    LOGIN_AGREE_RULE: 'I agree to the Terms of Service',
    LOGIN_AGREE: 'Agree',
    LOGIN_ENTRY_INFO1: 'New Member Registration (Free)',
    LOGIN_ENTRY_INFO2: 'Registration is required to bid on items.',
    LOGIN_CONFIRM_BUTTON: 'Sign in',
    LOGIN_PASSWORD_HINT: '8-16 alphanumeric characters',

    // Password Reminder
    LOGIN_REMINDER_TITLE: 'Forgot Your Password?',
    LOGIN_REMINDER_SUBTITLE: 'Reminder',
    LOGIN_REMINDER_MESSAGE1:
      'If you forgot your password, please enter your registered ID and email address.',
    LOGIN_REMINDER_MESSAGE2:
      'Press the "Send" button and your password will be sent to your registered email address.',
    LOGIN_REMINDER_FORGOT_ID: 'If you forgot your ID, please contact us via the ',
    LOGIN_REMINDER_CONTACT_LINK: 'contact form',
    LOGIN_REMINDER_CONTACT_SUFFIX: '.',
    LOGIN_REMINDER_USER_ID: 'ID',
    LOGIN_REMINDER_USER_ID_PLACEHOLDER: 'Alphanumeric characters',
    LOGIN_REMINDER_SEND_BUTTON: 'Send',
    LOGIN_REMINDER_COMPLETE_MESSAGE: 'A new password has been sent to your email address.',
    LOGIN_REMINDER_BACK_TO_LOGIN: 'Back to Login',
    LOGIN_REMINDER_EMAIL_ERROR: 'Email address is invalid',
    LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: 'Email addresses do not match',
    MYPAGE_EDIT_PROFILE: 'Edit profile',
    MYPAGE_EDIT_CONFIRM: 'Edit Confirmation',
    MYPAGE_CARD: 'Card information',

    // Existing uppercase keys (keep as-is)
    COMMON_BID_LABEL: 'Bid',
    BID_COUNT: '',
    CLASSIFICATION_ASCENDING: 'Ascending Auction',
    CLASSIFICATION_SEALED: 'Sealed Auction',
    ASCENDING: 'Ascending',
    SEALED: 'Sealed',
    BID_STATUS_INPROGRESS: 'In Progress',
    BID_STATUS_CANCEL: 'Suspension of exhibits',
    BID_STATUS_NOT_START_YET: 'Waiting to Start',
    BID_STATUS_ENDED: 'End',
    BID_STATUS_PRE_AUCTION: 'Pre-Auction',
    BID_STATUS_EXTENDING: 'Auction Extended',
    HIGHEST_BIDDER: 'Highest Bidder',
    HIGHEST_BID_AMOUNT: 'Highest Bid Amount',
    BID_STATUS: 'Status',
    REMAINING_TIME: 'Remaining time',
    YOU_ARE_TOP: 'Top bidder',
    RESERVE_PRICE_NOT_MET: 'The minimum bid price has not been reached.',
    RESERVE_PRICE_EXCEEDED: 'Exceeding the minimum required bid',
    MORE_LITTLE: 'Just A little more',
    SECOND_BIDDER: '2nd place bidder',
    END_DATE_TIME: 'End date and time',
    START_DATE_TIME: 'Start date and time',
    RECORDED_BID_PRICE: 'Your Price:',

    // Date picker
    DATE_PICKER_DATE_FORMAT: 'yyyyMMdd',
    DATE_PICKER_YEAR: 'Year',
    DATE_PICKER_MONTH: 'Month',
    DATE_PICKER_DAY: 'Day',

    // Custom translation(jp key)
    単位で入札できます: 'You can bid in units',
    終了予定: 'Scheduled End',
    この商品をシェア: 'Share this product',
  },
}

export interface TranslationKeys {
  $vuetify: {
    dataIterator: {
      rowsPerPageText: string
      pageText: string
    }
    [key: string]: any
  }

  // COMMON TRANSLATIONS
  COMMON_BACK: string
  COMMON_BACK_LIST: string
  COMMON_MORE: string
  COMMON_JAPAN: string
  COMMON_DUBAI: string
  COMMON_HONGKONG: string
  COMMON_SEND: string
  COMMON_AGREE: string
  COMMON_ERROR: string
  COMMON_CONFIRM: string
  COMMON_INPUT_ERROR: string
  COMMON_DATE_FORMAT: string
  COMMON_REMOVE: string
  COMMON_UPDATE_AUCTION: string
  COMMON_DAY: string
  COMMON_HOUR: string
  COMMON_MINUTE: string
  COMMON_SECOND: string
  ASCENDING_AUCTION: string
  SEALED_AUCTION: string

  // SITE INFORMATION
  SITE_TITLE: string
  COPYRIGHT: string

  // TOP PAGE APP BAR
  TOP_APP_BAR_SELECT_CATEGORY: string
  TOP_APP_BAR_REGISTER: string
  TOP_APP_BAR_LOGIN: string
  TOP_APP_BAR_LOGOUT: string

  // HEADER NAVIGATION
  HEADER_NAV_PRODUCT_CATEGORY: string
  HEADER_NAV_ALL_CATEGORIES: string
  HEADER_NAV_CATEGORY_1: string
  HEADER_NAV_CATEGORY_2: string
  HEADER_NAV_CATEGORY_3: string
  HEADER_NAV_SEARCH_PLACEHOLDER: string
  HEADER_NAV_SITE_ABOUT: string
  HEADER_NAV_FIRST_TIME_VISITORS: string
  HEADER_NAV_SHOPPING_GUIDE: string
  HEADER_NAV_FAQ: string
  HEADER_NAV_CONTACT_US: string
  HEADER_NAV_MEMBER_SERVICES: string
  HEADER_NAV_LOGIN: string
  HEADER_NAV_FAVORITES: string
  HEADER_NAV_BIDDING: string
  HEADER_NAV_SUCCESSFUL_BID_HISTORY: string
  HEADER_NAV_REGISTER: string
  HEADER_NAV_LOGOUT: string
  HEADER_NAV_NEW_MEMBER_REGISTER: string
  HEADER_NAV_MEMBER_MENU: string
  HEADER_NAV_MY_PAGE: string
  HEADER_NAV_GUIDANCE: string
  HEADER_NAV_COMPANY_INFO: string
  HEADER_NAV_TOKUSHO: string
  HEADER_NAV_TERMS_OF_SERVICE: string
  HEADER_NAV_SPECIFIED_COMMERCIAL_TRANSACTION_LAW: string
  HEADER_NAV_PRIVACY_POLICY: string

  // ROUTE/PAGE TITLES
  ROUTE_TOP: string

  // PRODUCT DETAIL PAGE
  DETAIL_TITLE: string
  DETAIL_DESCRIPTION: string
  DETAIL_CURRENCY: string
  DETAIL_QUANTITY: string
  DETAIL_LOWEST_BID_QUANTITY: string
  DETAIL_LOWEST_BID_PRICE: string
  DETAIL_BID_COUNT: string
  DETAIL_BID_QUANTITY: string
  DETAIL_BID_UNIT_PRICE: string
  DETAIL_BID_PRICE_FOR_ASC_AUCTION: string
  DETAIL_BID_TOTAL_PRICE: string
  DETAIL_BID_BUTTON: string
  DETAIL_CONTACT_BUTTON: string
  DETAIL_ABOUT_RANK: string
  DETAIL_CHAT: string
  DETAIL_VIEW_COMMENTS: string

  // PRODUCT DETAIL INFO SECTION
  DETAIL_INFO_MAKER: string
  DETAIL_INFO_PRODUCT_NAME: string
  DETAIL_INFO_SIM: string
  DETAIL_INFO_CAPACITY: string
  DETAIL_INFO_COLOR: string
  DETAIL_INFO_RANK: string
  DETAIL_INFO_QUANTITY: string
  DETAIL_INFO_NOTE1: string
  DETAIL_INFO_NOTE2: string
  DETAIL_INFO_LOWEST_BID_PRICE: string
  DETAIL_INFO_LOWEST_BID_QUANTITY: string
  DETAIL_INFO_FAVORITE: string
  DETAIL_INFO_START_PRICE: string
  DETAIL_INFO_CURRENT_PRICE: string
  DETAIL_INFO_TAX_INCLUDED_PRICE: string
  DETAIL_INSTANT_PRICE: string

  // PRODUCT DETAIL BID MODAL
  ERROR_BID_MODAL_LOGIN_REQUIRED_MESSAGE: string

  // PRODUCT DETAIL ERROR MESSAGES
  ERROR_PRICE_EMPTY: string
  ERROR_PRICE_INVALID_FORMAT: string
  ERROR_PRICE_MAX_LENGTH: string
  ERROR_LOWEST_UNIT_PRICE_INVALID: string
  ERROR_NEW_PRICE_LOWER_THAN_CURRENT_PRICE_ERR: string
  ERROR_QUANTITY_EMPTY: string
  ERROR_QUANTITY_INVALID_FORMAT: string
  ERROR_QUANTITY_MAX_LENGTH: string
  ERROR_LOWEST_BID_QUANTITY_INVALID: string
  ERROR_BID_QUANTITY_EXCEEDS_MAX: string

  // Filter box
  FILTER_BOX_TITLE: string
  FILTER_BOX_INPUT_PLACEHOLDER: string
  FILTER_BOX_KEYWORD: string
  FILTER_BOX_SEARCH_BUTTON: string
  FILTER_BOX_CATEGORY: string
  FILTER_BOX_CLEAR_CONDITIONS: string
  FILTER_BOX_AUCTION_COUNT: string
  FILTER_BOX_SEARCH_CRITERIA: string
  SEARCH_RESULT: string
  PRODUCT_LIST: string

  MYPAGE_AUCTION_COUNT: string

  // Favorite
  FAVORITE_TITLE: string
  FAVORITE_EMPTY: string
  FAVORITE_CLEAR_PRICE_INPUT: string
  FAVORITE_SUB_TOTAL_BID_PRICE_HEADER: string
  FAVORITE_SUB_TOTAL_BID_PRICE: string
  FAVORITE_LOGIN_REQUIRED_FAVORITE: string
  FAVORITE_BID_BUTTON: string
  FAVORITE_RE_BID_BUTTON: string
  FAVORITE_BID_QUANTITY: string
  FAVORITE_DELETE_FAVORITE1: string
  FAVORITE_DELETE_FAVORITE2: string

  // Bid history
  BID_HISTORY_END_DATE: string
  BID_HISTORY_BID_SUCCESS_UNIT_PRICE: string
  BID_HISTORY_BID_SUCCESS_PRICE: string
  BID_HISTORY_BID_SUCCESS_QUANTITY: string
  BID_HISTORY_BID_TOTAL_PRICE: string

  // Auth
  AUTH_LOGOUT_MESSAGE: string
  AUTH_LOGOUT: string
  AUTH_CLOSE: string
  AUTH_CANCEL: string

  // Login
  LOGIN_TITLE: string
  LOGIN_EMAIL: string
  LOGIN_EMAIL_CONFIRM: string
  LOGIN_PASSWORD: string
  LOGIN_SAVE_LOGIN_INFO: string
  LOGIN_FORGET_PASSWORD: string
  LOGIN_RULE: string
  LOGIN_AGREE_RULE: string
  LOGIN_AGREE: string
  LOGIN_ENTRY_INFO1: string
  LOGIN_ENTRY_INFO2: string
  LOGIN_CONFIRM_BUTTON: string
  LOGIN_PASSWORD_HINT: string

  // Password Reminder
  LOGIN_REMINDER_TITLE: string
  LOGIN_REMINDER_SUBTITLE: string
  LOGIN_REMINDER_MESSAGE1: string
  LOGIN_REMINDER_MESSAGE2: string
  LOGIN_REMINDER_FORGOT_ID: string
  LOGIN_REMINDER_CONTACT_LINK: string
  LOGIN_REMINDER_CONTACT_SUFFIX: string
  LOGIN_REMINDER_USER_ID: string
  LOGIN_REMINDER_USER_ID_PLACEHOLDER: string
  LOGIN_REMINDER_SEND_BUTTON: string
  LOGIN_REMINDER_COMPLETE_MESSAGE: string
  LOGIN_REMINDER_BACK_TO_LOGIN: string
  LOGIN_REMINDER_EMAIL_ERROR: string
  LOGIN_REMINDER_CONFIRM_EMAIL_ERROR: string
  MYPAGE_EDIT_PROFILE: string
  MYPAGE_EDIT_CONFIRM: string
  MYPAGE_CARD: string

  // AUCTION & BIDDING SYSTEM
  COMMON_BID_LABEL: string
  BID_COUNT: string
  CLASSIFICATION_ASCENDING: string
  CLASSIFICATION_SEALED: string
  ASCENDING: string
  SEALED: string
  BID_STATUS_INPROGRESS: string
  BID_STATUS_CANCEL: string
  BID_STATUS_NOT_START_YET: string
  BID_STATUS_ENDED: string
  BID_STATUS_PRE_AUCTION: string
  BID_STATUS_EXTENDING: string
  HIGHEST_BIDDER: string
  HIGHEST_BID_AMOUNT: string
  BID_STATUS: string
  REMAINING_TIME: string
  YOU_ARE_TOP: string
  RESERVE_PRICE_NOT_MET: string
  RESERVE_PRICE_EXCEEDED: string
  MORE_LITTLE: string
  SECOND_BIDDER: string
  END_DATE_TIME: string
  START_DATE_TIME: string
  RECORDED_BID_PRICE: string
  DATE_PICKER_DATE_FORMAT: string
  DATE_PICKER_YEAR: string
  DATE_PICKER_MONTH: string
  DATE_PICKER_DAY: string

  // Custom translation(jp key)
  単位で入札できます: string
  終了予定: string
  この商品をシェア: string
}

export interface Translate {
  ja: TranslationKeys
  en: TranslationKeys
}

// Extract all translation keys as a union type for type-safe t() function
export type TranslationKey = keyof Omit<TranslationKeys, '$vuetify'>

// Type for the t() function
export type TranslateFunction = (key: TranslationKey) => string

// Utility type to ensure both languages have the same keys
export type EnsureKeysMatch<T extends Record<string, TranslationKeys>> = {
  [K in keyof T]: TranslationKeys
} & T

// Type guard to ensure translate object has matching keys
/**
 *
 * @param translate
 */
export function validateTranslate<T extends Record<string, TranslationKeys>>(
  translate: EnsureKeysMatch<T>
): EnsureKeysMatch<T> {
  return translate
}
