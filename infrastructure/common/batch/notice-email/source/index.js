const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const {HeadObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);

const client = new S3Client({});
const pool = new PgPool();

const callSendNoticeHtmlEmail = (
  promise,
  argumentList,
  passData,
  splitAmount,
  waitSeconds = null
) => {
  if (argumentList.length === 0) {
    return Promise.resolve([]);
  }
  const runSplitPromise = (args, index, resultArray) => {
    console.log('index: ', index);
    if (index < args.length) {
      console.log('args: ', args[index]);
      return Promise.resolve()
        .then(() => {
          if (waitSeconds && index !== 0) {
            return Common.sleep(waitSeconds);
          }
          return Promise.resolve();
        })
        .then(() => {
          return promise(
            process.env.SEND_HTML_NOTICE_EMAIL_LAMBDA_ARN,
            {
              attFilesSize: passData.attFilesSize,
              noticeEmailNo: passData.notice_email_no,
              tenantNo: passData.tenant_no,
              langs: passData.langs,
              receivers: args[index],
            },
            {maxRetries: 0}
          );
        })
        .then(result => {
          resultArray.push(result);
          return runSplitPromise(args, index + 1, resultArray);
        });
    }
    return Promise.resolve(resultArray);
  };

  const splitNumberOfTimes = Math.ceil(argumentList.length / splitAmount);
  return runSplitPromise(
    Array(splitNumberOfTimes)
      .fill({})
      .map((tmp, index) => {
        return argumentList.slice(
          index * splitAmount,
          (index + 1) * splitAmount
        );
      }),
    0,
    []
  );
};

exports.handle = (e, ctx, cb) => {
  return Promise.resolve()
    .then(() => {
      return pool.byPassQuery('SYSTEM_BATCH','SELECT * FROM "f_get_notice_email_targets"();');
    })
    .then(result => {
      console.log(result);
      return Promise.resolve()
        .then(() => {
          // Update sent flag first
          return pool.byPassQuery('SYSTEM_BATCH',
            'SELECT * FROM "f_update_notice_email_sent_flag"($1);',
            [result.map(row => row.notice_email_no)]
          );
        })
        .then(() => {
          return Promise.all(
            result.map(notice => {
              return Promise.all(
                (notice.langs || []).map(lang => {
                  // Attachment files size
                  let attFilesSize = 0;

                  return Promise.resolve()
                    .then(() => {
                      // Get attachments size
                      return Promise.resolve()
                        .then(() => {
                          // Get attachments
                          return Promise.all(
                            (lang.file || []).map(key => {
                              const command = new HeadObjectCommand({
                                Bucket: process.env.S3_BUCKET,
                                Key: key,
                              });
                              return client.send(command).then(att => {
                                return Promise.resolve({
                                  filename: key.replace(/^.*[\\\/]/, ''),
                                  size: att.ContentLength,
                                });
                              });
                            })
                          );
                        })
                        .then(attFiles => {
                          console.log('attFiles: ', attFiles);
                          attFilesSize = 0;
                          attFiles.map(att => {
                            attFilesSize += Number(att.size) || 0;
                          });
                          console.log('attFilesSize: ', attFilesSize);
                          return Promise.resolve();
                        });
                    })
                    .then(() => {
                      // Get Member's email list
                      return pool
                        .byPassQuery(
                          'SYSTEM_BATCH',
                          'SELECT * FROM "f_get_notice_email_for_sending_email"($1,$2,$3);',
                          [
                            notice.tenant_no,
                            notice.notice_email_no,
                            lang.language_code,
                          ]
                        )
                        .then(noticeEmails => {
                          let receivers = [];
                          noticeEmails.map(noticeEmail => {
                            receivers = receivers.concat(
                              (noticeEmail.members || []).map(member => {
                                if (member.free_field.email) {
                                  return {
                                    language_code: noticeEmail.language_code,
                                    receivers: [member.free_field.email].filter(
                                      row => row
                                    ),
                                    bcc: [],
                                  };
                                }
                                return null;
                              })
                            );
                          });
                          return Promise.resolve(receivers.filter(row => row));
                        });
                    })
                    .then(receivers => {
                      console.log('receivers: ', receivers);
                      // 添付ファイルサイズの合計が3.5Mを超えてたら
                      // 呼びがわ：50件60秒
                      // 呼ばれる側：1件1秒
                      const splitAmount = 50;
                      let waitSeconds = 10;
                      if (attFilesSize > 3.5 * 1024 * 1024) {
                        waitSeconds = 60;
                      }
                      console.log('splitAmount: ', splitAmount);
                      console.log('waitSeconds: ', waitSeconds);
                      const passData = Object.assign({}, notice, {
                        attFilesSize,
                      });
                      // Send email
                      return callSendNoticeHtmlEmail(
                        Common.invokeLambdaEventType,
                        receivers,
                        passData,
                        splitAmount,
                        waitSeconds
                      );
                    });
                })
              );
            })
          );
        });
    })
    .then(result => {
      console.log(result);
      return Base.createSuccessResponse(cb, null);
    })
    .catch(error => {
      return Common.createErrorResponse(cb, error);
    });
};
