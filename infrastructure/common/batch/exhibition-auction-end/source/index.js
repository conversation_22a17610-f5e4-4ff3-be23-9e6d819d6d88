const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

const pool = new PgPool()
const readOnly = new PgPool(process.env.READ_ONLY_PGHOST)

// Batch functions need a system tenant ID for cross-tenant operations
const SYSTEM_TENANT_ID = 'SYSTEM_BATCH'

exports.handle = (e, ctx, cb) => {
  return Promise.resolve()
    .then(() => {
      return pool.byPassQuery(
        SYSTEM_TENANT_ID,
        'SELECT * FROM "f_batch_exhibition_auction_end"();'
      )
    })
    .then(exhibitions => {
      console.log('f_batch_exhibition_auction_end: ', exhibitions)
      if (exhibitions.length === 0) {
        return Promise.resolve()
      }
      return Promise.resolve()
        .then(() => {
          console.log('Check exhibition end email option')
          return pool.byPassQuery(
            SYSTEM_TENANT_ID,
            'SELECT * FROM "f_batch_get_exhibition_end_email_option"($1);',
            [exhibitions.map(row => row.tenant_no)]
          )
        })
        .then(sendEmailSettings => {
          console.log('sendEmailSettings: ', sendEmailSettings)

          if (!sendEmailSettings || sendEmailSettings.length <= 0) {
            return Promise.resolve()
          }

          return Promise.all(
            sendEmailSettings.map(setting => {
              const tenantNo = setting.tenant_no
              const isSendEmail = setting.exhibition_end_email_option === 2 // 2 means "send email on exhibition end"

              console.log('isSendEmail: ', isSendEmail)
              if (!isSendEmail) {
                return Promise.resolve()
              }

              console.log('SEND EXHIBITION END EMAIL')
              return readOnly
                .rlsQuery(
                  tenantNo,
                  'SELECT * FROM "f_get_member_info_for_exhibition_end_notification"($1);',
                  [exhibitions.map(row => row.exhibition_no)]
                )
                .then(result => {
                  return readOnly
                    .rlsQuery(
                      tenantNo,
                      'SELECT * FROM "f_get_constant_by_key_string"($1,$2);',
                      [
                        tenantNo,
                        [
                          'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER',
                          'EMAIL_COMMON_FOOTER',
                          'MAIL_FROM',
                        ],
                      ]
                    )
                    .then(constants => {
                      return Common.splitAll(
                        Common.sendMailBySES,
                        result.map(member => {
                          const mailFrom =
                            constants.find(
                              x =>
                                x.key_string === 'EMAIL_FROM' &&
                                x.language_code === member.language_code
                            )?.value2 || null
                          const mail =
                            constants.find(
                              x =>
                                x.key_string ===
                                  'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER' &&
                                x.language_code === member.language_code
                            ) || {}
                          const footer =
                            constants.find(
                              x =>
                                x.key_string === 'EMAIL_COMMON_FOOTER' &&
                                x.language_code === member.language_code
                            ) || {}
                          const title = mail.value1
                          const sender = mailFrom
                            ? `"${mailFrom}"<${mail.value2}>`
                            : mail.value2
                          const receivers = [member.member_field.email]

                          const bcc = []
                          const value5s = mail.value5.split(',')
                          const content = Common.format(mail.value4, [
                            member.member_field.companyName,
                            member.exhibition_name,
                            [
                              member.items.filter(item => item.successful)
                                .length > 0
                                ? value5s[0]
                                : '', // 落札
                              member.items
                                .filter(item => item.successful)
                                .map(item => {
                                  return [
                                    item.item_field.product_name
                                      ? `${value5s[2]}: ${item.item_field.product_name}`
                                      : null,
                                  ]
                                    .filter(row => row)
                                    .join('\n')
                                })
                                .filter(row => row)
                                .join('\n'),
                              member.items.filter(item => item.successful)
                                .length > 0 &&
                              member.items.filter(item => !item.successful)
                                .length > 0
                                ? '\n'
                                : '',
                              member.items.filter(item => !item.successful)
                                .length > 0
                                ? value5s[1]
                                : '', // 不落
                              member.items
                                .filter(item => !item.successful)
                                .map(item => {
                                  return [
                                    item.item_field.product_name
                                      ? `${value5s[2]}: ${item.item_field.product_name}`
                                      : null,
                                  ]
                                    .filter(row => row)
                                    .join('\n')
                                })
                                .filter(row => row)
                                .join('\n'),
                            ]
                              .filter(row => row)
                              .join('\n'),
                            footer.value4,
                          ])
                          return [title, content, sender, receivers, bcc]
                        }),
                        200,
                        2
                      )
                    })
                })
            })
          )
        })
        .then(() => {
          return Promise.all(
            exhibitions.map(exh => {
              const tenantNo = exh.tenant_no
              return pool.rlsQuery(
                tenantNo,
                'SELECT * FROM "f_update_exhibition_item_linked_flag"($1);',
                [exh.exhibition_no]
              )
            })
          )
        })
    })
    .then(result => {
      return cb(null, null)
    })
    .catch(error => {
      return Common.createErrorResponse(cb, error)
    })
}
