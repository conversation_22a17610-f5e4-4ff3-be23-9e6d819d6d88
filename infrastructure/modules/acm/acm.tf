provider "aws" {
  region  = var.region
  profile = var.profile_name
}


# Enhanced ACM certificate management with proper cross-zone validation
locals {
  # Include both custom domains and the primary ACM domain for zone mapping
  custom_domains_list = var.custom_domains != "" ? split(",", var.custom_domains) : []
  primary_domain      = var.acm_domain_name != "" ? [var.acm_domain_name] : []
  # Deduplicate domains to prevent duplicate key errors in the domain_to_best_zone map
  all_domains_list    = distinct(concat(local.primary_domain, local.custom_domains_list))
  hosted_zone_names   = keys(var.zone_map)

  # Create comprehensive domain to zone mapping including primary domain
  domain_to_best_zone = {
    for domain in local.all_domains_list : domain => (
      endswith(domain, "stage.auction.custom-ec.com") ? var.zone_map["stage.auction.custom-ec.com"] : (
        endswith(domain, "stage.auction.custom-ec.jp") ? var.zone_map["stage.auction.custom-ec.jp"] : ""
      )
    )
  }

  # Prepare certificate configuration (only custom domains as SANs, excluding primary domain)
  subject_alternative_names = [
    for domain in local.custom_domains_list : domain
    if domain != var.acm_domain_name
  ]
}

# Create single ACM certificate with all domains (CloudFront requires single cert)
resource "aws_acm_certificate" "acm_cert" {
  count                     = var.acm_domain_name != "" ? 1 : 0
  domain_name               = var.acm_domain_name
  subject_alternative_names = local.subject_alternative_names
  validation_method         = "DNS"

  tags = {
    Name = "${var.project_name}-${var.environment}"
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Create DNS validation records with proper zone mapping
resource "aws_route53_record" "acm_cert" {
  for_each = {
    for dvo in (length(aws_acm_certificate.acm_cert) > 0 ? aws_acm_certificate.acm_cert[0].domain_validation_options : []) : dvo.domain_name => {
      name    = dvo.resource_record_name
      record  = dvo.resource_record_value
      type    = dvo.resource_record_type
      zone_id = lookup(local.domain_to_best_zone, dvo.domain_name, "")
    }
    if lookup(local.domain_to_best_zone, dvo.domain_name, "") != ""
  }

  name    = each.value.name
  records = [each.value.record]
  type    = each.value.type
  zone_id = each.value.zone_id
  ttl     = 60

  lifecycle {
    create_before_destroy = true
  }

  depends_on = [aws_acm_certificate.acm_cert]
}

# Certificate validation
resource "aws_acm_certificate_validation" "acm_cert" {
  count                   = length(aws_acm_certificate.acm_cert) > 0 ? 1 : 0
  certificate_arn         = aws_acm_certificate.acm_cert[0].arn
  validation_record_fqdns = [for record in aws_route53_record.acm_cert : record.fqdn]
  depends_on              = [aws_route53_record.acm_cert]
}
