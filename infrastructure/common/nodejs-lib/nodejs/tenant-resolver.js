const TENANT_DOMAIN_MAP = {
  // 管理サイト (admin-side) domain
    'https://do268epc5wmsb.cloudfront.net': 1,
    'https://d18bnb2abbw8ps.cloudfront.net': 1,
    'https://saas-1-admin.stage.auction.custom-ec.com': 1,
    'https://saas-2-admin.stage.auction.custom-ec.jp': 2,

  // オークションサイト (auction-side) domain
    'https://d18yxra1tfyhwr.cloudfront.net': 1,
    'http://localhost:3000': 1, // for local development(admin-side)
    'http://localhost:5173': 1, // for local development(auction-side)
    'http://localhost:5174': 1, // for local development(auction-side)
    'http://localhost:5175': 1, // for local development(auction-side)
    'https://saas-1-auction.stage.auction.custom-ec.com': 1, // New stage.auction.custom-ec.com domains
    'https://saas-2-auction.stage.auction.custom-ec.jp': 2 // New stage.auction.custom-ec.com domains
}

/**
 * Tenant resolver utility
 * Identifies tenant based on origin
 */
function resolveTenant(event) {
  console.log('find tenant ID from origin path!')
  const origin = event.headers?.origin
  if (origin && TENANT_DOMAIN_MAP[origin]) {
    console.log(`Tenant resolved from hostname: ${origin}`)
    return {
      error: false,
      data: TENANT_DOMAIN_MAP[origin],
    }
  }

  const errorResponse = {
    error: true,
    message: 'Unable to resolve domain',
  }
  console.error(
    'Unable to resolve tenant from domain: ' + (origin || 'unknown')
  )
  return errorResponse
}

module.exports = {
  resolveTenant,
}
