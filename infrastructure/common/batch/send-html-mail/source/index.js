const {GetObjectCommand, S3Client} = require('@aws-sdk/client-s3');
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);

exports.handle = (e, ctx, cb) => {
  console.log(e);
  const params = e.body;
  const s3 = new S3Client({});
  // TODO
  const tenantNo = 1;

  return Promise.resolve()
    .then(() => {
      const notice = {
        tenantNo,
        exhibitionName: params.exhibitionName,
        attFilesSize: params.attFilesSize || 0,
        receivers: params.receivers,
        langs: params.langs,
        exhInfos: params.exhInfos || [],
      };
      return Promise.resolve(notice);
    })
    .then(notice => {
      console.log('Exhibition Notice: ', notice);

      if (notice === null || typeof notice === 'undefined') {
        return Promise.resolve([]);
      }

      return Promise.all(
        (notice.langs || []).map(lang => {
          return pool
            .byPassQuery('SYSTEM_BATCH','SELECT * FROM "f_get_constant_by_key_string"($1,$2,$3);', [
              tenantNo,
              ['EXHIBITION_AUCTION_NOTICE_EMAIL', 'EMAIL_FROM'],
              lang.language_code,
            ])
            .then(constants => {
              console.log(constants);
              const mailFrom =
                constants.find(x => x.key_string === 'EMAIL_FROM')?.value2 ||
                null;
              const htmlConstant =
                constants.find(
                  x => x.key_string === 'EXHIBITION_AUCTION_NOTICE_EMAIL'
                ) || {};
              const sender = mailFrom
                ? `"${mailFrom}"<${htmlConstant.value2}>`
                : htmlConstant.value2;

              let body = lang ? lang.body : '';
              body = Common.replaceURLs(body);
              body = body ? body.replace(/\r\n|\r|\n/g, '<br />') : '';

              const title = lang ? lang.title : '';
              const files = lang ? lang.file : [];
              const receivers = notice.receivers
                ? notice.receivers.filter(
                    x => x.language_code === lang.language_code
                  )
                : [];

              const exhInfos =
                notice.exhInfos.filter(
                  x => x.language_code === lang.language_code
                ) || [];
              const exhInfo = exhInfos.length > 0 ? exhInfos[0] : {};

              // Get email template html file
              return Promise.resolve()
                .then(() => {
                  // htmlを使用しない
                  // return s3.getObject({
                  //   Bucket : process.env['S3_BUCKET'],
                  //   Key    : htmlConstant.file_url
                  // }).promise()
                  return Promise.resolve(null);
                })
                .then(data => {
                  console.log(receivers);

                  // Get attachments and send email
                  return Promise.resolve()
                    .then(() => {
                      // Get attachments
                      return Promise.all(
                        (files || []).map(key => {
                          const command = new GetObjectCommand({
                            Bucket: process.env.S3_BUCKET,
                            Key: key,
                          });
                          return s3
                            .send(command)
                            .then(att => {
                              return att.Body?.transformToByteArray();
                            })
                            .then(buf => {
                              return Promise.resolve({
                                filename: key.replace(/^.*[\\\/]/, ''),
                                content: Buffer.from(buf, 'binary'),
                              });
                            });
                        })
                      );
                    })
                    .then(attFiles => {
                      // 添付ファイルサイズの合計が3.5Mを超えてたら
                      // 呼びがわ：50件60秒
                      // 呼ばれる側：1件1秒
                      let splitAmount = 10;
                      let waitSeconds = 1;
                      if (notice.attFilesSize > 3.5 * 1024 * 1024) {
                        splitAmount = 1;
                        waitSeconds = 1;
                      }
                      console.log('notice.attFilesSize: ', notice.attFilesSize);
                      console.log('splitAmount: ', splitAmount);
                      console.log('waitSeconds: ', waitSeconds);

                      // Send email with attachments
                      return Common.splitAll(
                        Common.sendRawMailBySES,
                        receivers.map(receiver => {
                          const context = body ? body : '';
                          return [
                            title,
                            context,
                            sender,
                            receiver.receivers,
                            receiver.bcc,
                            attFiles,
                            true,
                          ];
                        }),
                        splitAmount,
                        waitSeconds
                      );
                    });
                });
            });
        })
      );
    })
    .then(result => {
      console.log(result);
      return cb(null, {});
    })
    .catch(error => {
      return Common.createErrorResponse(cb, error);
    });
};

const replaceContext = (context, object) => {
  let modifiedContext = context;
  Object.keys(object).map(key => {
    modifiedContext = modifiedContext.split(`##${key}`).join(object[key]);
  });
  return modifiedContext;
};
