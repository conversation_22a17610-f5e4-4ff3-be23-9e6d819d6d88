const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)
const moment = require('moment')
const pool = new PgPool()

// Batch functions need a system tenant ID for cross-tenant operations
const SYSTEM_TENANT_ID = 'SYSTEM_BATCH'

exports.handle = (e, ctx, cb) => {
  moment.locale('ja')
  return Promise.resolve()
    .then(() => {
      return pool.byPassQuery(
        SYSTEM_TENANT_ID,
        'SELECT * FROM "f_batch_exhibition_item_auction_end"();'
      )
    })
    .then(exhs => {
      console.log('ENDED AUCTION NOTIFICATION EMAIL: ', exhs)
      if (typeof exhs === 'undefined' || exhs === null || exhs.length === 0) {
        return Promise.resolve()
      }

      // Accumulate distinct tenant_no list from exhibitions
      const tenantNos = []
      exhs.forEach(exh => {
        if (!tenantNos.includes(exh.tenant_no)) {
          tenantNos.push(exh.tenant_no)
        }
      })
      console.log('tenantNos: ', tenantNos)

      return Promise.all(
        tenantNos.map(tenant_no => {
          console.log('tenant_no: ', tenant_no)

          const exhibitions = exhs.filter(exh => exh.tenant_no === tenant_no)
          console.log('exhibitions: ', exhibitions)

          return Promise.resolve()
            .then(() => {
              console.log('Check exhibition end email option')
              return pool
                .rlsQuery(
                  tenant_no,
                  'SELECT * FROM "f_batch_get_exhibition_end_email_option"($1);',
                  [tenant_no]
                )
                .then(data => {
                  if (data && data.length > 0) {
                    // 随時終了の場合はメールを送る
                    if (data[0].exhibition_end_email_option === 2) {
                      return Promise.resolve(1)
                    }
                  }
                  return Promise.resolve(0)
                })
            })
            .then(isSendEmail => {
              console.log('isSendEmail: ', isSendEmail)
              if (isSendEmail === 0) {
                return Promise.resolve()
              }
              console.log('SEND EXHIBITION END EMAIL')
              return pool
                .rlsQuery(
                  tenant_no,
                  'SELECT * FROM "f_get_member_info_for_exhibition_item_end_notification"($1);',
                  [exhibitions.map(row => row.exhibition_item_no)]
                )
                .then(result => {
                  return pool
                    .rlsQuery(
                      tenant_no,
                      'SELECT * FROM "f_get_constant_by_key_string"($1,$2);',
                      [
                        tenant_no,
                        [
                          'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER',
                          'EMAIL_COMMON_FOOTER',
                          'TAX_RATE',
                          'MAIL_FROM',
                        ],
                      ]
                    )
                    .then(constants => {
                      return Promise.all(
                        result
                          .filter(
                            x =>
                              x.items.filter(item => item.successful).length > 0
                          )
                          .map(member => {
                            const lang = member.language_code
                            const taxRate =
                              constants.find(
                                constant =>
                                  constant.key_string === 'TAX_RATE' &&
                                  constant.language_code ===
                                    member.language_code
                              ) || {}
                            const footer =
                              constants.find(
                                constant =>
                                  constant.key_string ===
                                    'EMAIL_COMMON_FOOTER' &&
                                  constant.language_code ===
                                    member.language_code
                              ) || {}
                            const receivers = [member.member_field.email]
                            const bcc = []
                            let sender = null

                            // Item list
                            const itemList = member.items.filter(
                              item => item.successful
                            )
                            return Common.splitAll(
                              Common.sendMailBySES,
                              itemList.map(item => {
                                // Bid price
                                const bid_success_price_tax = taxRate?.value1
                                  ? item.bid_success_price +
                                    item.bid_success_price *
                                      (Number(taxRate.value1) / 100)
                                  : item.bid_success_price

                                let content = ''
                                let title = ''

                                const mail =
                                  constants.find(
                                    constant =>
                                      constant.key_string ===
                                        'EMAIL_EXHIBITION_AUCTION_END_FOR_MEMBER' &&
                                      constant.language_code ===
                                        member.language_code
                                  ) || {}
                                sender = mail.value2
                                const value5s = mail.value5.split(',')
                                const item_bid_success_price = Common.format(
                                  value5s[4],
                                  [
                                    Common.numberStringWithComma(
                                      item.bid_success_price
                                    ),
                                  ]
                                )
                                const item_bid_success_price_tax =
                                  Common.format(value5s[4], [
                                    Common.numberStringWithComma(
                                      bid_success_price_tax
                                    ),
                                  ])

                                content = Common.format(mail.value4, [
                                  member.member_field.memberName, // 会員名
                                  item.item_field.productName || '', // 商品名
                                  item_bid_success_price || '', // 落札金額
                                  item_bid_success_price_tax || '', // 税込
                                  item.manage_no, // 商品URL
                                  footer.value4,
                                ])

                                // Title: (商品名)。。。
                                title = `${item.item_field.productName} ${mail.value1}`

                                return [title, content, sender, receivers, bcc]
                              }),
                              200,
                              2
                            )
                          })
                      )
                    })
                })
            })
        })
      )
    })
    .then(result => {
      return cb(null, null)
    })
    .catch(error => {
      return Common.createErrorResponse(cb, error)
    })
}
