CREATE OR REPLACE FUNCTION public.f_batch_get_exhibition_end_email_option (
    in_tenant_nos bigint[]
)
RETURNS TABLE(
    tenant_no bigint,
    exhibition_end_email_option integer
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 終了した開催回ステータス更新
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT T.tenant_no, T.exhibition_end_email_option FROM m_tenant T WHERE T.tenant_no = ANY(in_tenant_nos);

END;

$BODY$;
