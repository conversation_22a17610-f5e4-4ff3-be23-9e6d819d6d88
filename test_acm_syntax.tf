# Test file to validate ACM module syntax changes
locals {
  # Test the deduplication logic
  custom_domains_list = ["domain1.com", "domain2.com", "domain1.com"]
  primary_domain      = ["domain1.com"]

  # This should produce ["domain1.com", "domain2.com"] without duplicates
  all_domains_list    = distinct(concat(local.primary_domain, local.custom_domains_list))

  # Test the conditional SAN logic
  acm_domain_name = "domain1.com"
  subject_alternative_names = [
    for domain in local.custom_domains_list : domain
    if domain != local.acm_domain_name
  ]
}

# Output to verify the logic works
output "test_all_domains" {
  value = local.all_domains_list
}

output "test_sans" {
  value = local.subject_alternative_names
}
