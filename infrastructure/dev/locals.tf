locals {
  project_name                         = "saas"
  environment                          = "dev"
  profile_name                         = "saas-dev"
  basic_auth_enable                    = true
  admin_basic_auth_enable              = false
  external_domain_name                 = false
  record_name                          = ""
  auction_domain_name                  = "" # Use for create SES
  admin_domain_name                    = ""
  mail_from_domain                     = ""
  web_socket_domain                    = ""
  nat_gateway_amount                   = 1
  serverless_min_capacity              = 0 # Minimum for cost optimization
  serverless_max_capacity              = 4.0 # Moderate max for dev environment
  enable_read_replica                  = true # Enable read replica for read-only proxy endpoint
  api_gateway_5xx_alarm_slack_hook_url = "*******************************************************************************"

  admin_lambda_global_environment_variables = {
    "JWT_KEY" : "9989073198427807137232231262089010020123587350290187348031312887018790938534411834407306789251908665247269492987924177522229233751002949748257274877665617761140060607452136458097592753547729486234167197152010786269050707025330716247130358546954092845095476",
    "MAX_RESPONSE_SIZE" : "5300000"
  }

  auction_lambda_global_environment_variables = {
    "JWT_KEY" : "3482073198427807137232231262089010020123587350290187348031312887016756756734411834407306789251908665247269492987924177522229233751002949748257274877665617761140060607452136458097592753547729486234167197152010786269050707025330716247130358546954092845095476",
    "MAX_RESPONSE_SIZE" : "5300000"
  }

  admin_acm_domain_name = "saas-1-admin.stage.auction.custom-ec.com" // 代表結合ドメイン
  admin_tenants         = {
    tenant1 = {
      tenant_id        = "1"
      use_custom_domain = true
      domain_name       = "saas-1-admin.stage.auction.custom-ec.com"
      hosted_zone_name  = "stage.auction.custom-ec.com"
    }
    tenant2 = {
      tenant_id        = "2"
      use_custom_domain = true
      domain_name       = "saas-2-admin.stage.auction.custom-ec.jp"
      hosted_zone_name  = "stage.auction.custom-ec.jp"
    }
  }
  admin_custom_domains  = [
    "saas-1-admin.stage.auction.custom-ec.com",
    "saas-2-admin.stage.auction.custom-ec.jp"
  ]

  auction_acm_domain_name = "saas-1-auction.stage.auction.custom-ec.com" // 代表結合ドメイン
  auction_tenants         = {
    tenant1 = {
      tenant_id        = "1"
      use_custom_domain = true
      domain_name       = "saas-1-auction.stage.auction.custom-ec.com"
      hosted_zone_name  = "stage.auction.custom-ec.com"
    }
    tenant2 = {
      tenant_id        = "2"
      use_custom_domain = true
      domain_name       = "saas-2-auction.stage.auction.custom-ec.jp"
      hosted_zone_name  = "stage.auction.custom-ec.jp"
    }
  }
  auction_custom_domains  = [
    "saas-1-auction.stage.auction.custom-ec.com",
    "saas-2-auction.stage.auction.custom-ec.jp"
  ]
}
