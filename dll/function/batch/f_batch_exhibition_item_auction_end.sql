CREATE OR REPLACE FUNCTION public.f_batch_exhibition_item_auction_end ()
RETURNS TABLE(
    tenant_no bigint,
    exhibition_result_no bigint,
    exhibition_item_no bigint
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 終了した出品ステータス更新
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH target_items AS (
    SELECT TEI.exhibition_item_no,
           TEI.tenant_no,
            TE.exhibition_no,
            TE.exhibition_classification_info->'auctionClassification' AS auction_classification,
            TL.lot_no,
            TL.lot_id,
            TEI.lowest_bid_accept_price,
            TEI.lowest_bid_accept_quantity,
            TEI.cancel_flag,
            CASE WHEN TE.exhibition_classification_info->'auctionClassification' = '1'
                      THEN json_build_array(
                        json_build_object(
                        'top_member_no', TEI.top_member_no,
                        'current_price', TEI.current_price,
                        'top_price', TEI.top_price,
                        'bid_quantity', 1,
                        'allocated_quantity', 1
                      ))
                ELSE (SELECT json_agg(
                      json_build_object(
                        'top_member_no', top_member_no,
                        'current_price', top_price,
                        'top_price', top_price,
                        'bid_quantity', bid_quantity,
                        'allocated_quantity', allocated_quantity
                      ))
                      FROM "f_get_top_bid_order" (TEI.exhibition_item_no)
                    )
           END results,
          (
            SELECT array_agg(json_build_object(
                    'item_no', TI.item_no,
                    'manage_no', TI.manage_no,
                    'order_no', TLD.order_no,
                    'item_field', TIL.free_field
                   ))
               FROM t_lot_detail TLD
               JOIN t_item TI
                 ON TLD.item_no = TI.item_no
                AND TI.tenant_no = TEI.tenant_no
                AND (TI.delete_flag IS NULL OR TI.delete_flag = 0)
               JOIN t_item_localized TIL
                 ON TIL.item_no = TI.item_no
                AND TIL.tenant_no = TEI.tenant_no
                AND TIL.language_code = 'ja'
                AND (TIL.delete_flag IS NULL OR TIL.delete_flag = 0)
              WHERE TLD.lot_no = TEI.lot_no
            ) items
        FROM t_exhibition_item TEI
        JOIN t_exhibition TE
          ON TEI.exhibition_no = TE.exhibition_no
         AND TEI.tenant_no = TE.tenant_no
         AND (TE.delete_flag IS NULL OR TE.delete_flag = 0)
        JOIN t_lot TL
          ON TL.lot_no = TEI.lot_no
         AND TL.tenant_no = TE.tenant_no
         AND (TL.delete_flag IS NULL OR TL.delete_flag = 0)
       WHERE (CASE
                WHEN TEI.end_datetime > COALESCE(TEI.default_end_datetime, TEI.end_datetime) THEN TEI.end_datetime
                ELSE COALESCE(TEI.default_end_datetime, TEI.end_datetime)
              END) < now()
         AND TEI.hummer_flag = 0
         AND (TEI.delete_flag IS NULL OR TEI.delete_flag = 0)
       LIMIT 1000
    ),
  update_unsuccessful AS (
    UPDATE t_exhibition_item TEI
       SET bid_success_member_no = NULL,
           hummer_flag = 2,
           update_datetime = now()
      FROM target_items TI
     WHERE TEI.exhibition_item_no = TI.exhibition_item_no
       AND (
        CASE WHEN TI.auction_classification = '1'
          THEN (
            COALESCE((
              SELECT (elem->>'top_price')::numeric
                FROM json_array_elements(TI.results) elem
                LIMIT 1
            ), 0) < TI.lowest_bid_accept_price
          )
          ELSE (
            COALESCE((
              SELECT (elem->>'top_price')::numeric
                FROM json_array_elements(TI.results) elem
                LIMIT 1
            ), 0) < TI.lowest_bid_accept_price

            OR COALESCE((
              SELECT (elem->>'bid_quantity')::numeric
                FROM json_array_elements(TI.results) elem
                LIMIT 1
            ), 0) < TI.lowest_bid_accept_quantity
          )
        END

        OR TI.cancel_flag = 1
       )
      RETURNING
        TEI.exhibition_item_no,
        TEI.bid_success_member_no,
        TEI.hummer_flag,
        TEI.end_datetime
  ),
  update_successful AS (
    UPDATE t_exhibition_item TEI
        SET bid_success_member_no = (
              SELECT (elem->>'top_member_no')::bigint
              FROM json_array_elements(TI.results) elem
              LIMIT 1
            ),
            top_member_no = (
              SELECT (elem->>'top_member_no')::bigint
              FROM json_array_elements(TI.results) elem
              LIMIT 1
            ),
            current_price = (
              SELECT (elem->>'current_price')::numeric
              FROM json_array_elements(TI.results) elem
              LIMIT 1
            ),
            top_price = (
              SELECT (elem->>'top_price')::numeric
              FROM json_array_elements(TI.results) elem
              LIMIT 1
            ),
           hummer_flag = 1,
           update_datetime = now()
      FROM target_items TI
     WHERE TEI.exhibition_item_no = TI.exhibition_item_no
       AND TEI.hummer_flag = 0
       AND (
         TEI.exhibition_item_no NOT IN (
           SELECT update_unsuccessful.exhibition_item_no FROM update_unsuccessful
         )
       )
      RETURNING
        TEI.exhibition_item_no,
        TEI.bid_success_member_no,
        TEI.hummer_flag,
        TEI.end_datetime
  ),
  get_tax_rate AS (
    SELECT TI.exhibition_item_no,
           MCL.value1::numeric AS tax_rate
      FROM target_items TI
      LEFT OUTER JOIN (
        SELECT mc.tenant_no
            , mcl.value1
            , mcl.value2
            , mcl.language_code
          FROM m_constant mc
          INNER JOIN m_constant_localized mcl ON mcl.constant_no = mc.constant_no
        WHERE mcl.language_code = 'ja'
          AND mc.key_string = 'TAX_RATE'
          AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
                        AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      ) MCL ON MCL.tenant_no = TI.tenant_no
  ),
  prepare_data_for_result AS (
    SELECT TI.tenant_no,
            TI.exhibition_no,
            TI.exhibition_item_no,
            TI.lot_no,
            TI.item_no,
            TI.lot_id,
            TI.manage_no,
            TI.item_field,
            RESULT.hummer_flag,
            TR.tax_rate,
            null::jsonb[] as options,
            CASE WHEN RESULT.hummer_flag = 1
                  THEN LPAD((COALESCE(TERD_CNT.cnt, 0) + 1)::text,2,'0')
                ELSE NULL
            END AS order_no
      FROM (
        SELECT target_items.*,
                (UNNEST(target_items.items)->>'item_no')::bigint AS item_no,
                (UNNEST(target_items.items)->>'item_field')::jsonb AS item_field,
                UNNEST(target_items.items)->>'manage_no' AS manage_no,
                (UNNEST(target_items.items)->>'order_no')::bigint AS order_no
          FROM target_items
      ) TI
      JOIN (
        SELECT * FROM update_successful
          UNION
        SELECT * FROM update_unsuccessful
      ) RESULT
        ON RESULT.exhibition_item_no = TI.exhibition_item_no
      -- Get tax rate --
      LEFT JOIN get_tax_rate TR
        ON TR.exhibition_item_no = TI.exhibition_item_no
      LEFT JOIN (
        SELECT TERD_C.bid_success_member_no,
                to_char(TERD_C.create_datetime, 'yyyy/mm/dd') AS create_datetime,
                COUNT(*) as cnt
          FROM t_exhibition_result_detail TERD_C
          WHERE TERD_C.bid_success_member_no IS NOT NULL
          GROUP BY TERD_C.bid_success_member_no,
                  to_char(TERD_C.create_datetime, 'yyyy/mm/dd')
      ) TERD_CNT
        ON TERD_CNT.bid_success_member_no = RESULT.bid_success_member_no
        AND TERD_CNT.create_datetime = to_char(now(), 'yyyy/mm/dd')
      ORDER BY TI.lot_id, TI.manage_no
  ),
  prepare_data_for_result_detail AS (
    SELECT TI.tenant_no,
            TI.exhibition_item_no,
            RESULT.hummer_flag,
            CASE WHEN RESULT.hummer_flag = 1 THEN MM.free_field
                 ELSE NULL
            END AS member_field,
            CASE WHEN RESULT.hummer_flag = 1 THEN (TI.result->>'top_member_no')::bigint
                 ELSE NULL
            END AS bid_success_member_no,
            CASE WHEN RESULT.hummer_flag = 1
                      THEN (TI.result->>'top_price')::numeric
                ELSE NULL
            END AS bid_success_price,
            CASE WHEN RESULT.hummer_flag = 1 THEN (TI.result->>'allocated_quantity')::integer
                 ELSE NULL
            END AS bid_success_quantity
      FROM (
        SELECT target_items.*,
                json_array_elements(target_items.results) AS result
          FROM target_items
      ) TI
      JOIN (
        SELECT * FROM update_successful
          UNION
        SELECT * FROM update_unsuccessful
      ) RESULT
        ON RESULT.exhibition_item_no = TI.exhibition_item_no

      LEFT JOIN m_member MM
        ON MM.member_no = (TI.result->>'top_member_no')::bigint
        AND MM.tenant_no = TI.tenant_no
        AND (MM.delete_flag IS NULL OR MM.delete_flag = 0)

      ORDER BY TI.exhibition_item_no
  ),
  insert_exhibition_result AS (
    INSERT INTO t_exhibition_result (
      tenant_no,
      exhibition_no,
      exhibition_item_no,
      lot_no,
      item_no,
      lot_id,
      manage_no,
      item_field,
      hummer_flag,
      tax_rate,
      options,
      order_no,
      create_datetime
    )
    (
      SELECT TI.tenant_no,
              TI.exhibition_no,
              TI.exhibition_item_no,
              TI.lot_no,
              TI.item_no,
              TI.lot_id,
              TI.manage_no,
              TI.item_field,
              TI.hummer_flag,
              TI.tax_rate,
              TI.options,
              TI.order_no,
              now()
        FROM prepare_data_for_result TI
    )
    RETURNING
      t_exhibition_result.tenant_no,
      t_exhibition_result.exhibition_result_no,
      t_exhibition_result.exhibition_item_no
  ),
  insert_exhibition_result_detail AS (
    INSERT INTO t_exhibition_result_detail (
      tenant_no,
      exhibition_result_no,
      member_field,
      bid_success_member_no,
      bid_success_quantity,
      bid_success_price
    )
    (
      SELECT TI.tenant_no,
              IER.exhibition_result_no,
              TI.member_field,
              TI.bid_success_member_no,
              TI.bid_success_quantity,
              TI.bid_success_price
        FROM prepare_data_for_result_detail TI
        JOIN insert_exhibition_result IER
          ON IER.exhibition_item_no = TI.exhibition_item_no
        WHERE TI.hummer_flag = 1
    )
    RETURNING t_exhibition_result_detail.exhibition_result_detail_no, t_exhibition_result_detail.exhibition_result_no
  )

  SELECT IER.tenant_no, IER.exhibition_result_no, IER.exhibition_item_no FROM insert_exhibition_result IER;
END;

$BODY$;
